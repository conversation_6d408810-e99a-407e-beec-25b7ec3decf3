# Manticore Search Configuration

# ===== Manticore 连接配置 =====
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_USER=
MANTICORE_PASSWORD=
MANTICORE_CHARSET=utf8mb4

# ===== 连接池配置 =====
MANTICORE_CONNECTION_POOL_SIZE=10
MANTICORE_CONNECTION_TIMEOUT=30
MANTICORE_READ_TIMEOUT=30

# ===== 搜索配置 =====
MANTICORE_DEFAULT_SEARCH_LIMIT=20
MANTICORE_MAX_SEARCH_LIMIT=100
MANTICORE_SNIPPET_LENGTH=200
MANTICORE_SNIPPET_AROUND=5

# ===== 向量搜索配置 =====
MANTICORE_VECTOR_DIMENSIONS=1536
MANTICORE_KNN_SEARCH_LIMIT=10

# ===== HNSW 算法配置 =====
MANTICORE_HNSW_M=16
MANTICORE_HNSW_EF_CONSTRUCTION=200
MANTICORE_HNSW_SIMILARITY=cosine

# ===== 日志配置 =====
MANTICORE_LOG_LEVEL=INFO
MANTICORE_LOG_FORMAT=json

# ===== API 配置 =====
MANTICORE_API_HOST=0.0.0.0
MANTICORE_API_PORT=9000
MANTICORE_API_TITLE="Manticore Search API"
MANTICORE_API_DESCRIPTION="基于 Manticore Search 的高内聚搜索引擎接口"
MANTICORE_API_VERSION="1.0.0"
MANTICORE_API_PREFIX="/api/v1"

# ===== 健康检查配置 =====
MANTICORE_HEALTH_CHECK_TIMEOUT=5

# ===== 表配置 =====
MANTICORE_DEFAULT_TABLE_NAME=knowledge_base
