home = /opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/bin
include-system-site-packages = false
version = 3.11.13
executable = /opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/bin/python3.11
command = /Users/<USER>/claude-workspace/master-know/embedding_service/venv/bin/python3.11 -m venv /Users/<USER>/claude-workspace/master-know/topic_service/venv
