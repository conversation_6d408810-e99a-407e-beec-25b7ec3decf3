"""
向量化服务配置管理

简化版配置，避免复杂的依赖关系
"""

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from typing import Optional
from functools import lru_cache
import os


class EmbeddingServiceSettings(BaseSettings):
    """向量化服务配置类"""

    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9001,
        description="向量化服务端口"
    )
    api_title: str = Field(
        default="Embedding Service API",
        description="向量化服务API标题"
    )
    api_description: str = Field(
        default="文本向量化和相似度计算服务",
        description="向量化服务API描述"
    )
    api_version: str = Field(
        default="2.0.0",
        description="API版本"
    )

    # ===== OpenAI API 配置 =====
    openai_api_key: str = Field(
        default="",
        description="OpenAI API密钥"
    )
    openai_base_url: str = Field(
        default="https://api.openai.com/v1",
        description="OpenAI API基础URL"
    )
    default_model: str = Field(
        default="text-embedding-ada-002",
        description="默认向量化模型"
    )
    request_timeout: int = Field(
        default=30,
        description="请求超时时间(秒)"
    )
    max_retries: int = Field(
        default=3,
        description="最大重试次数"
    )

    # ===== 缓存配置 =====
    cache_ttl: int = Field(
        default=3600,  # 1小时
        description="缓存过期时间(秒)"
    )
    enable_cache: bool = Field(
        default=False,  # MVP阶段暂时关闭
        description="是否启用缓存"
    )

    @field_validator('openai_api_key')
    @classmethod
    def validate_openai_key(cls, v):
        if not v:
            import warnings
            warnings.warn("OpenAI API密钥未设置，服务可能无法正常工作", UserWarning)
        return v

    @field_validator('openai_base_url')
    @classmethod
    def validate_openai_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("OpenAI API URL必须以http://或https://开头")
        return v.rstrip('/')

    # ===== 额外配置 =====
    api_host: str = Field(
        default="0.0.0.0",
        description="API服务主机"
    )
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis连接URL"
    )
    log_level: str = Field(
        default="INFO",
        description="日志级别"
    )

    model_config = {
        "env_prefix": "EMBEDDING_",
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"  # 忽略额外字段
    }


# 为了向后兼容，保留Settings别名
Settings = EmbeddingServiceSettings

@lru_cache()
def get_settings() -> EmbeddingServiceSettings:
    """获取向量化服务配置实例（单例模式）"""
    return EmbeddingServiceSettings()


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 向量化服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"OpenAI模型: {settings.default_model}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
