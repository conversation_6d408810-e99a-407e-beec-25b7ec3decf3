import logging
import sys
import os

# 添加当前目录和父目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional

# 使用绝对导入
try:
    from services.embedding_service import EmbeddingService
    from models.embedding import (
        EmbeddingRequest,
        EmbeddingResponse,
        LegacyEmbeddingRequest,
        LegacyEmbeddingResponse
    )
    from utils.config import get_settings, Settings
except ImportError as e:
    print(f"Import error: {e}")
    # 如果绝对导入失败，尝试相对导入
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from services.embedding_service import EmbeddingService
    from models.embedding import (
        EmbeddingRequest,
        EmbeddingResponse,
        LegacyEmbeddingRequest,
        LegacyEmbeddingResponse
    )
    from utils.config import get_settings, Settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Embedding Service",
    version="2.0.0",
    description="OpenAI compatible embedding service"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局服务实例 (避免重复初始化)
_embedding_service = None

async def get_embedding_service() -> EmbeddingService:
    global _embedding_service
    if _embedding_service is None:
        settings = get_settings()
        _embedding_service = EmbeddingService(settings)
    return _embedding_service

@app.post("/api/v1/embeddings", response_model=EmbeddingResponse)
async def create_embeddings(
    request: EmbeddingRequest,
    service: EmbeddingService = Depends(get_embedding_service)
):
    """
    Create embeddings for input text(s) - OpenAI compatible endpoint
    """
    try:
        logger.info(f"Received embedding request for model: {request.model}")
        response = await service.create_embeddings(request)
        logger.info(f"Successfully created {len(response.data)} embeddings")
        return response
    except Exception as e:
        logger.error(f"Error in create_embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 向后兼容的端点 (已弃用)
@app.post("/api/v1/embed", response_model=LegacyEmbeddingResponse, deprecated=True)
async def embed_text_legacy(
    request: LegacyEmbeddingRequest,
    service: EmbeddingService = Depends(get_embedding_service)
):
    """
    Legacy embedding endpoint (deprecated) - use /api/v1/embeddings instead
    """
    try:
        logger.warning("Using deprecated /api/v1/embed endpoint")
        return await service.embed_text_legacy(request)
    except Exception as e:
        logger.error(f"Error in embed_text_legacy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/health")
async def health_check():
    """
    Health check endpoint
    """
    try:
        settings = get_settings()
        return {
            "status": "healthy",
            "service": "embedding_service",
            "version": "2.0.0",
            "base_url": settings.openai_base_url,
            "default_model": settings.default_model
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "service": "embedding_service",
            "error": str(e)
        }

@app.get("/api/v1/models")
async def list_models():
    """
    List available models (placeholder)
    """
    settings = get_settings()
    return {
        "object": "list",
        "data": [
            {
                "id": settings.default_model,
                "object": "model",
                "created": **********,
                "owned_by": "openai"
            }
        ]
    }

@app.post("/api/v1/embeddings/store")
async def store_embeddings(
    texts: List[str],
    table_name: str = "embeddings",
    model: Optional[str] = None,
    metadata: Optional[List[Dict[str, Any]]] = None,
    service: EmbeddingService = Depends(get_embedding_service)
):
    """
    生成向量并存储到Manticore Search
    """
    try:
        logger.info(f"Storing embeddings for {len(texts)} texts in table {table_name}")

        # 生成向量
        embeddings = await service.embed_batch_texts(texts, model)

        # 存储到Manticore
        success = await service.store_embeddings_in_manticore(
            texts=texts,
            embeddings=embeddings,
            table_name=table_name,
            metadata=metadata
        )

        return {
            "success": success,
            "stored_count": len(texts) if success else 0,
            "table_name": table_name,
            "message": "Embeddings stored successfully" if success else "Failed to store embeddings"
        }

    except Exception as e:
        logger.error(f"Error storing embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/embeddings/search")
async def search_similar(
    query_text: str,
    table_name: str = "embeddings",
    limit: int = 10,
    model: Optional[str] = None,
    service: EmbeddingService = Depends(get_embedding_service)
):
    """
    搜索相似向量
    """
    try:
        logger.info(f"Searching for similar embeddings to: {query_text[:50]}...")

        # 生成查询向量
        query_embedding = await service.embed_single_text(query_text, model)

        # 搜索相似向量
        results = await service.search_similar_embeddings(
            query_embedding=query_embedding,
            table_name=table_name,
            limit=limit
        )

        return {
            "query": query_text,
            "results": results,
            "count": len(results),
            "table_name": table_name
        }

    except Exception as e:
        logger.error(f"Error searching embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
