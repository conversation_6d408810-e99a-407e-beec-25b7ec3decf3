import asyncio
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Union, Optional, Dict, Any
import httpx
from openai import AsyncOpenAI
from models.embedding import (
    EmbeddingRequest,
    EmbeddingResponse,
    EmbeddingObject,
    Usage,
    LegacyEmbeddingRequest,
    LegacyEmbeddingResponse
)
from utils.config import Settings

# 导入manticore_search模块 (可选)
try:
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'manticore_search'))
    from clients.manticore_client import ManticoreClient
    from utils.config import get_settings as get_manticore_settings
    MANTICORE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Manticore Search not available: {e}")
    ManticoreClient = None
    get_manticore_settings = None
    MANTICORE_AVAILABLE = False

logger = logging.getLogger(__name__)

class EmbeddingService:
    def __init__(self, settings: Settings):
        self.settings = settings

        # 初始化 OpenAI 客户端
        self.client = AsyncOpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            timeout=settings.request_timeout,
            max_retries=settings.max_retries
        )

        # 初始化 Manticore 客户端
        try:
            manticore_settings = get_manticore_settings()
            self.manticore_client = ManticoreClient(manticore_settings)
            self.manticore_enabled = True
            logger.info("Manticore Search client initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Manticore client: {e}")
            self.manticore_client = None
            self.manticore_enabled = False

        logger.info(f"EmbeddingService initialized with base_url: {settings.openai_base_url}")

    async def create_embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """
        Create embeddings using OpenAI compatible API
        """
        try:
            # 确保 input 是列表格式
            if isinstance(request.input, str):
                input_texts = [request.input]
            else:
                input_texts = request.input

            logger.info(f"Creating embeddings for {len(input_texts)} texts using model {request.model}")

            # 调用 OpenAI API
            response = await self.client.embeddings.create(
                input=input_texts,
                model=request.model,
                encoding_format=request.encoding_format.value if request.encoding_format else "float",
                dimensions=request.dimensions,
                user=request.user
            )

            # 转换为我们的响应格式
            embedding_objects = []
            for i, embedding_data in enumerate(response.data):
                embedding_objects.append(EmbeddingObject(
                    embedding=embedding_data.embedding,
                    index=i
                ))

            usage = Usage(
                prompt_tokens=response.usage.prompt_tokens,
                total_tokens=response.usage.total_tokens
            )

            return EmbeddingResponse(
                data=embedding_objects,
                model=response.model,
                usage=usage
            )

        except Exception as e:
            logger.error(f"Error creating embeddings: {str(e)}")
            raise

    async def embed_single_text(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        便捷方法：为单个文本生成向量
        """
        request = EmbeddingRequest(
            input=text,
            model=model or self.settings.default_model
        )

        response = await self.create_embeddings(request)
        return response.data[0].embedding

    async def embed_batch_texts(self, texts: List[str], model: Optional[str] = None) -> List[List[float]]:
        """
        便捷方法：为多个文本生成向量
        """
        request = EmbeddingRequest(
            input=texts,
            model=model or self.settings.default_model
        )

        response = await self.create_embeddings(request)
        return [obj.embedding for obj in response.data]

    async def store_embeddings_in_manticore(
        self,
        texts: List[str],
        embeddings: List[List[float]],
        table_name: str = "embeddings",
        metadata: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        将向量存储到Manticore Search中
        """
        if not self.manticore_enabled:
            logger.warning("Manticore Search not available, skipping storage")
            return False

        try:
            # 确保表存在
            await self._ensure_embedding_table_exists(table_name)

            # 准备文档数据
            documents = []
            import time
            import uuid
            base_timestamp = int(time.time() * 1000000)

            for i, (text, embedding) in enumerate(zip(texts, embeddings)):
                # 生成更安全的唯一ID
                doc_id = base_timestamp + i

                doc = {
                    'id': doc_id,
                    'text': text,
                    'embedding': embedding,  # 直接使用向量数组，不转换为字符串
                    'dimension': len(embedding)
                }

                # 添加元数据
                if metadata and i < len(metadata):
                    doc.update(metadata[i])

                documents.append(doc)

            # 批量插入
            success_count = self.manticore_client.bulk_insert_documents(table_name, documents)
            logger.info(f"Successfully stored {success_count}/{len(documents)} embeddings in Manticore")
            return success_count == len(documents)

        except Exception as e:
            logger.error(f"Failed to store embeddings in Manticore: {e}")
            return False

    async def search_similar_embeddings(
        self,
        query_embedding: List[float],
        table_name: str = "embeddings",
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        在Manticore Search中搜索相似向量
        注意：这是一个简化版本，实际的向量相似度搜索需要更复杂的实现
        """
        if not self.manticore_enabled:
            logger.warning("Manticore Search not available")
            return []

        try:
            # 这里使用文本搜索作为示例，实际应该使用向量相似度搜索
            # 在生产环境中，需要实现KNN搜索或使用Manticore的向量搜索功能
            results = self.manticore_client.search_documents(
                table_name=table_name,
                query="*",  # 获取所有文档作为示例
                limit=limit
            )

            logger.info(f"Found {len(results)} similar embeddings")
            return results

        except Exception as e:
            logger.error(f"Failed to search similar embeddings: {e}")
            return []

    async def _ensure_embedding_table_exists(self, table_name: str) -> bool:
        """
        确保向量表存在
        """
        try:
            if not self.manticore_client.table_exists(table_name):
                # 获取配置的向量维度
                from manticore_search.utils.config import get_settings
                manticore_settings = get_settings()
                vector_dims = manticore_settings.vector_dimensions

                # 创建向量表的schema - 使用float_vector而非text
                schema = f"""
                id bigint,
                text text,
                embedding float_vector knn_type='hnsw' knn_dims='{vector_dims}' hnsw_similarity='cosine',
                dimension int,
                created_at timestamp default now()
                """

                self.manticore_client.create_table(table_name, schema)
                logger.info(f"Created embedding table: {table_name} with {vector_dims} dimensions")

            return True

        except Exception as e:
            logger.error(f"Failed to ensure table exists: {e}")
            return False

    # 向后兼容的方法 (已弃用)
    async def embed_text_legacy(self, request: LegacyEmbeddingRequest) -> LegacyEmbeddingResponse:
        """
        向后兼容的单文本向量化方法 (已弃用)
        """
        logger.warning("Using deprecated embed_text_legacy method")

        embedding = await self.embed_single_text(
            text=request.text,
            model=request.model
        )

        return LegacyEmbeddingResponse(
            embedding=embedding,
            dimension=len(embedding),
            processing_time=0.0,  # 不再计算处理时间
            model=request.model or self.settings.default_model
        )
